package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.ArrayList;

/**
 * 문제 카테고리 모델
 * - quiz_category 테이블에 대응
 * - 계층형 구조 지원 (부모-자식 관계)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuizCategory {

    /**
     * 카테고리 고유 ID
     */
    private Long categoryId;

    /**
     * 부모 카테고리 ID
     * - 최상위 카테고리는 NULL
     * - 서브 카테고리는 부모 카테고리의 ID를 가짐
     */
    private Long parentId;

    /**
     * 카테고리명
     * - 사용자가 직접 입력하는 카테고리명
     * - 최대 100자까지 입력 가능
     */
    private String categoryName;

    /**
     * 카테고리 설명
     */
    private String description;

    /**
     * 생성자 (user_email)
     */
    private String createId;

    /**
     * 생성일시
     */
    private java.time.LocalDateTime createDate;

    /**
     * 최종수정자 (user_email)
     */
    private String lastUpdateId;

    /**
     * 최종수정일시
     */
    private java.time.LocalDateTime lastUpdateDate;

    /**
     * 삭제여부
     */
    private String deleteYn;

    /**
     * 삭제자 (user_email)
     */
    private String deleteId;

    /**
     * 삭제일시
     */
    private java.time.LocalDateTime deleteDate;

    // ========== 계층형 구조를 위한 추가 필드 ==========

    /**
     * 부모 카테고리 정보
     * - 조회 시에만 사용되는 필드 (DB 저장 X)
     */
    private QuizCategory parent;

    /**
     * 자식 카테고리 목록
     * - 조회 시에만 사용되는 필드 (DB 저장 X)
     */
    @Builder.Default
    private List<QuizCategory> children = new ArrayList<>();

    /**
     * 부모 카테고리명
     * - 리스트 조회 시 표시용 (DB 저장 X)
     */
    private String parentCategoryName;

    /**
     * 계층 깊이
     * - 최상위 카테고리: 0, 1단계 서브: 1, 2단계 서브: 2...
     * - 조회 시에만 사용되는 필드 (DB 저장 X)
     */
    private Integer depth;

    /**
     * 계층 경로
     * - 예: "상위카테고리 > 중간카테고리 > 현재카테고리"
     * - 조회 시에만 사용되는 필드 (DB 저장 X)
     */
    private String categoryPath;

    /**
     * 자식 카테고리 개수
     * - 조회 시에만 사용되는 필드 (DB 저장 X)
     */
    private Integer childrenCount;

    // ========== 계층형 구조 관련 유틸리티 메서드 ==========

    /**
     * 최상위 카테고리인지 확인
     * @return 최상위 카테고리 여부
     */
    public boolean isRootCategory() {
        return this.parentId == null;
    }

    /**
     * 자식 카테고리가 있는지 확인
     * @return 자식 카테고리 존재 여부
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }

    /**
     * 자식 카테고리 추가
     * @param child 자식 카테고리
     */
    public void addChild(QuizCategory child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
        child.setParent(this);
    }
}
