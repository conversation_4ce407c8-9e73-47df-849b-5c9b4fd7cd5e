<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QuizMapper">

    <!-- 기본 ResultMap -->
    <resultMap id="QuizResultMap" type="kr.wayplus.qr_hallimpark.model.Quiz">
        <id property="quizId" column="quiz_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="quizType" column="quiz_type"/>
        <result property="difficultyLevel" column="difficulty_level"/>
        <result property="correctAnswer" column="correct_answer"/>
        <result property="imageUrl" column="image_url"/>
        <result property="hint" column="hint"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
        <!-- quiz_content 필드 -->
        <result property="question" column="question"/>
        <result property="options" column="options"/>
        <!-- 조인 필드 -->
        <result property="categoryName" column="category_name"/>
        <result property="parentCategoryName" column="parent_category_name"/>
        <result property="categoryPath" column="category_path"/>
        <result property="languageSupport" column="language_support"/>
        <result property="qrMappingCount" column="qr_mapping_count"/>
    </resultMap>

    <!-- 공통 SELECT 절 -->
    <sql id="selectQuizColumns">
        qm.quiz_id,
        qm.category_id,
        qm.quiz_type,
        qm.difficulty_level,
        qm.correct_answer,
        qm.image_url,
        qm.hint,
        qm.create_id,
        qm.create_date,
        qm.last_update_id,
        qm.last_update_date,
        qm.delete_yn,
        qm.delete_id,
        qm.delete_date,
        qc.question,
        qc.options,
        cat.category_name,
        parent_cat.category_name AS parent_category_name,
        CASE 
            WHEN parent_cat.category_name IS NOT NULL 
            THEN CONCAT(parent_cat.category_name, ' > ', cat.category_name)
            ELSE cat.category_name
        END AS category_path,
        COALESCE(lang_info.language_support, '한국어') AS language_support,
        COALESCE(qr_info.qr_mapping_count, 0) AS qr_mapping_count
    </sql>

    <!-- 공통 FROM 절 -->
    <sql id="fromQuizTables">
        FROM quiz_master qm
        LEFT JOIN quiz_content qc ON qm.quiz_id = qc.quiz_id AND qc.lang_code = 'ko'
        LEFT JOIN quiz_category cat ON qm.category_id = cat.category_id
        LEFT JOIN quiz_category parent_cat ON cat.parent_id = parent_cat.category_id
        LEFT JOIN (
            SELECT 
                qc_lang.quiz_id,
                GROUP_CONCAT(
                    CASE qc_lang.lang_code
                        WHEN 'ko' THEN '한국어'
                        WHEN 'en' THEN '영어'
                        WHEN 'ja' THEN '일본어'
                        WHEN 'zh' THEN '중국어'
                        ELSE qc_lang.lang_code
                    END
                    ORDER BY qc_lang.lang_code
                    SEPARATOR ', '
                ) AS language_support
            FROM quiz_content qc_lang
            WHERE qc_lang.delete_yn = 'N'
            GROUP BY qc_lang.quiz_id
        ) lang_info ON qm.quiz_id = lang_info.quiz_id
        LEFT JOIN (
            SELECT 
                qqm.quiz_id,
                COUNT(*) AS qr_mapping_count
            FROM qr_quiz_mapping qqm
            GROUP BY qqm.quiz_id
        ) qr_info ON qm.quiz_id = qr_info.quiz_id
    </sql>

    <!-- 공통 WHERE 절 -->
    <sql id="whereQuizConditions">
        WHERE qm.delete_yn = 'N'
        AND cat.delete_yn = 'N'
    </sql>

    <!-- 모든 문제 목록 조회 -->
    <select id="selectQuizList" resultMap="QuizResultMap">
        SELECT <include refid="selectQuizColumns"/>
        <include refid="fromQuizTables"/>
        <include refid="whereQuizConditions"/>
        ORDER BY qm.quiz_id DESC
    </select>

    <!-- 문제 ID로 문제 조회 -->
    <select id="selectQuizById" parameterType="long" resultMap="QuizResultMap">
        SELECT <include refid="selectQuizColumns"/>
        <include refid="fromQuizTables"/>
        <include refid="whereQuizConditions"/>
        AND qm.quiz_id = #{quizId}
    </select>

    <!-- 특정 카테고리의 문제 목록 조회 -->
    <select id="selectQuizListByCategoryId" parameterType="long" resultMap="QuizResultMap">
        SELECT <include refid="selectQuizColumns"/>
        <include refid="fromQuizTables"/>
        <include refid="whereQuizConditions"/>
        AND qm.category_id = #{categoryId}
        ORDER BY qm.quiz_id DESC
    </select>

    <!-- 특정 카테고리와 그 하위 카테고리의 문제 목록 조회 -->
    <select id="selectQuizListByCategoryIdWithChildren" parameterType="long" resultMap="QuizResultMap">
        SELECT <include refid="selectQuizColumns"/>
        <include refid="fromQuizTables"/>
        <include refid="whereQuizConditions"/>
        AND (qm.category_id = #{categoryId} 
             OR qm.category_id IN (
                 SELECT category_id 
                 FROM quiz_category 
                 WHERE parent_id = #{categoryId} 
                 AND delete_yn = 'N'
             ))
        ORDER BY qm.quiz_id DESC
    </select>

    <!-- 공통 리스트 기능: 동적 조건에 따른 리스트 조회 -->
    <select id="selectListWithConditions" parameterType="kr.wayplus.qr_hallimpark.model.AdminListSearch" resultMap="QuizResultMap">
        SELECT <include refid="selectQuizColumns"/>
        <include refid="fromQuizTables"/>
        <include refid="whereQuizConditions"/>
        
        <!-- 동적 검색 조건 -->
        <if test="searchFields != null and searchFields.size() > 0 and searchKeyword != null and searchKeyword.trim() != ''">
            AND (
            <foreach collection="searchFields" item="field" separator=" OR ">
                <choose>
                    <when test="field == 'question'">
                        qc.question LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="field == 'category_name'">
                        cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="field == 'quiz_type'">
                        qm.quiz_type LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                </choose>
            </foreach>
            )
        </if>
        
        <!-- 카테고리 필터 조건 (filters 맵 사용) -->
        <if test="filters != null and filters.containsKey('categoryFilter')">
            AND qm.category_id IN (
                SELECT category_id FROM quiz_category
                WHERE (category_id = #{filters.categoryFilter} OR parent_id = #{filters.categoryFilter})
                AND delete_yn = 'N'
            )
        </if>
        
        <!-- 정렬 -->
        <choose>
            <when test="sortField != null and sortField.trim() != ''">
                ORDER BY 
                <choose>
                    <when test="sortField == 'quiz_id'">qm.quiz_id</when>
                    <when test="sortField == 'question'">qc.question</when>
                    <when test="sortField == 'category_name'">cat.category_name</when>
                    <when test="sortField == 'quiz_type'">qm.quiz_type</when>
                    <when test="sortField == 'create_date'">qm.create_date</when>
                    <when test="sortField == 'last_update_date'">qm.last_update_date</when>
                    <otherwise>qm.quiz_id</otherwise>
                </choose>
                <choose>
                    <when test="sortDirection != null and sortDirection.toUpperCase() == 'ASC'">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY qm.quiz_id DESC
            </otherwise>
        </choose>
        
        <!-- 페이징 -->
        <if test="offset != null and size != null">
            LIMIT #{size} OFFSET #{offset}
        </if>
    </select>

    <!-- 공통 리스트 기능: 동적 조건에 따른 전체 개수 조회 -->
    <select id="countWithConditions" parameterType="kr.wayplus.qr_hallimpark.model.AdminListSearch" resultType="long">
        SELECT COUNT(DISTINCT qm.quiz_id)
        <include refid="fromQuizTables"/>
        <include refid="whereQuizConditions"/>
        
        <!-- 동적 검색 조건 -->
        <if test="searchFields != null and searchFields.size() > 0 and searchKeyword != null and searchKeyword.trim() != ''">
            AND (
            <foreach collection="searchFields" item="field" separator=" OR ">
                <choose>
                    <when test="field == 'question'">
                        qc.question LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="field == 'category_name'">
                        cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="field == 'quiz_type'">
                        qm.quiz_type LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                </choose>
            </foreach>
            )
        </if>
        
        <!-- 카테고리 필터 조건 (filters 맵 사용) -->
        <if test="filters != null and filters.containsKey('categoryFilter')">
            AND qm.category_id IN (
                SELECT category_id FROM quiz_category
                WHERE (category_id = #{filters.categoryFilter} OR parent_id = #{filters.categoryFilter})
                AND delete_yn = 'N'
            )
        </if>
    </select>

    <!-- 최상위 카테고리 목록 조회 (문제 관리용) -->
    <select id="selectRootCategoriesForQuizManagement" resultType="kr.wayplus.qr_hallimpark.model.QuizCategory">
        SELECT
            category_id,
            category_name,
            description
        FROM quiz_category
        WHERE parent_id IS NULL
        AND delete_yn = 'N'
        ORDER BY category_name ASC
    </select>

    <!-- 카테고리별 문제 개수 조회 -->
    <select id="countQuizByCategoryId" parameterType="long" resultType="int">
        SELECT COUNT(*)
        FROM quiz_master qm
        WHERE qm.category_id = #{categoryId}
        AND qm.delete_yn = 'N'
    </select>

    <!-- 문제명 중복 체크 -->
    <select id="countDuplicateQuestion" resultType="int">
        SELECT COUNT(*)
        FROM quiz_master qm
        LEFT JOIN quiz_content qc ON qm.quiz_id = qc.quiz_id AND qc.lang_code = 'ko'
        WHERE qc.question = #{question}
        AND qm.delete_yn = 'N'
        <if test="excludeQuizId != null">
            AND qm.quiz_id != #{excludeQuizId}
        </if>
    </select>

</mapper>
