#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3548), pid=2988, tid=16360
#
# JRE version:  (17.0.12+8) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:TieredStopAtLevel=1 -Dfile.encoding=x-windows-949 -Duser.country=KR -Duser.language=ko -Duser.variant kr.wayplus.qr_hallimpark.QrHallimparkApplication

Host: AMD RYZEN AI MAX+ 395 w/ Radeon 8060S          , 32 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Wed Jun 25 09:59:50 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 1.135658 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x00000201305b1110):  JavaThread "Unknown thread" [_thread_in_vm, id=16360, stack(0x000000f556500000,0x000000f556600000)]

Stack: [0x000000f556500000,0x000000f556600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67a04a]
V  [jvm.dll+0x7da4ed]
V  [jvm.dll+0x7dbe33]
V  [jvm.dll+0x7dc4a3]
V  [jvm.dll+0x24508f]
V  [jvm.dll+0x677089]
V  [jvm.dll+0x66bd32]
V  [jvm.dll+0x301fa6]
V  [jvm.dll+0x309546]
V  [jvm.dll+0x359d2e]
V  [jvm.dll+0x359f5f]
V  [jvm.dll+0x2d9078]
V  [jvm.dll+0x2d9fe4]
V  [jvm.dll+0x7aca21]
V  [jvm.dll+0x367591]
V  [jvm.dll+0x78b999]
V  [jvm.dll+0x3ec83f]
V  [jvm.dll+0x3ee471]
C  [jli.dll+0x5297]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd70267a18, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000020130662a80 GCTaskThread "GC Thread#0" [stack: 0x000000f556600000,0x000000f556700000] [id=6336]
  0x00000201305cfc40 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000f556700000,0x000000f556800000] [id=34984]
  0x000002013066dce0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000f556800000,0x000000f556900000] [id=17192]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd6fa99b67]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000201305ac970] Heap_lock - owner thread: 0x00000201305b1110

Heap address: 0x0000000603800000, size: 8136 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000603800000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd6fe83859]

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (1 events):
Event: 0.006 Loaded shared library C:\Program Files\Java\jdk-17\bin\java.dll


Dynamic libraries:
0x00007ff7dd410000 - 0x00007ff7dd420000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ffe3d800000 - 0x00007ffe3da65000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe3b6e0000 - 0x00007ffe3b7a9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe3a950000 - 0x00007ffe3ad38000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe3b160000 - 0x00007ffe3b2ab000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe26cb0000 - 0x00007ffe26cc9000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ffe242f0000 - 0x00007ffe2430b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ffe3bb10000 - 0x00007ffe3bbc3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe3b5b0000 - 0x00007ffe3b659000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe3d070000 - 0x00007ffe3d116000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe3c210000 - 0x00007ffe3c325000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe3b7b0000 - 0x00007ffe3b97a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe3b130000 - 0x00007ffe3b157000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe3c0e0000 - 0x00007ffe3c10b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe3add0000 - 0x00007ffe3af07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe3b4f0000 - 0x00007ffe3b593000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe19da0000 - 0x00007ffe1a03a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffe29990000 - 0x00007ffe2999b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe3d120000 - 0x00007ffe3d150000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe2c310000 - 0x00007ffe2c31c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ffe07200000 - 0x00007ffe0728e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ffd6f7b0000 - 0x00007ffd70390000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ffe3d160000 - 0x00007ffe3d168000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffe299a0000 - 0x00007ffe299aa000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffe3d1e0000 - 0x00007ffe3d254000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe2f1f0000 - 0x00007ffe2f225000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe39740000 - 0x00007ffe3975b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe26ca0000 - 0x00007ffe26caa000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ffe2a860000 - 0x00007ffe2aaa1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe3c330000 - 0x00007ffe3c6b5000 	C:\WINDOWS\System32\combase.dll
0x00007ffe3d4e0000 - 0x00007ffe3d5c1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe29540000 - 0x00007ffe29579000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe3af10000 - 0x00007ffe3afa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe16550000 - 0x00007ffe16575000 	C:\Program Files\Java\jdk-17\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-17\bin\server

VM Arguments:
jvm_args: -XX:TieredStopAtLevel=1 -Dfile.encoding=x-windows-949 -Duser.country=KR -Duser.language=ko -Duser.variant 
java_command: kr.wayplus.qr_hallimpark.QrHallimparkApplication
java_class_path (initial): D:\com\2025\HanLim\qr_hallimpark\build\classes\java\main;D:\com\2025\HanLim\qr_hallimpark\build\resources\main;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-devtools\3.4.6\cd3d90561142ed4fbffabe8e86348c29765420a\spring-boot-devtools-3.4.6.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mybatis.spring.boot\mybatis-spring-boot-starter\3.0.4\2a56c4aa1f81aaef9abb0105f72702a737517d9a\mybatis-spring-boot-starter-3.0.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.thymeleaf.extras\thymeleaf-extras-springsecurity6\3.1.3.RELEASE\f2a9e9505b145d22a52047460c74fb8200f2cb23\thymeleaf-extras-springsecurity6-3.1.3.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\nz.net.ultraq.thymeleaf\thymeleaf-layout-dialect\3.3.0\275da01788302e514c0549a4db5fa6b29ef84ba6\thymeleaf-layout-dialect-3.3.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mysql\mysql-connector-j\9.1.0\5fb1d513278e1a9767dfa80ea9d8d7ee909f1a\mysql-connector-j-9.1.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mariadb.jdbc\mariadb-java-client\3.4.2\b6333387fbddd2e4684e2f34a1e00265655bd426\mariadb-java-client-3.4.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mybatis.spring.boot\mybatis-spring-boot-autoconfigure\3.0.4\27c887cbd92c780e7d8baaf30d9dc5b5d5f0c03\mybatis-spring-boot-autoconfigure-3.0.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-autoconfigure\3.4.6\1fe47ca6f27ea5c1827817977ef3c181e99bf1fc\spring-boot-autoconfigure-3.4.6.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot\3.4.6\c98d5f56412a101f55f9e333dadf153ff8c63be2\spring-boot-3.4.6.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-config\6.4.6\79665e745bfee01a15c90bf0695d5b5b45d73f9e\spring-security-config-6.4.6.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-w
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 15                                        {product} {ergonomic}
     uint ConcGCThreads                            = 6                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 23                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8531214336                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8531214336                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;c:\Program Files\HP\1E.Performance.Assist\Extensibility\NomadBranch;C:\Program Files\HP\HP One Agent;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\dotnet\;C:\Program Files\Bandizip\;C:\Program Files\Java\jdk-17\bin\;C:\Program Files\Apache Software Foundation\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin\;;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=CJY
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 26 Model 112 Stepping 0, AuthenticAMD



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 4 days 0:40 hours
Hyper-V role detected

CPU: total 32 (initial active 32) (32 cores per cpu, 2 threads per core) family 26 model 112 stepping 0 microcode 0xb700032, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 32538M (10849M free)
TotalPageFile size 36634M (AvailPageFile size 75M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 87M, peak: 599M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286) for windows-amd64 JRE (17.0.12+8-LTS-286), built on Jun  5 2024 06:46:59 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
